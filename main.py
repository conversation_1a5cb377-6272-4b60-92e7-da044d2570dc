from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from bs4 import BeautifulSoup
import time
import openpyxl
import csv
import os
from openpyxl import Workbook
from webdriver_manager.chrome import ChromeDriverManager
from random import uniform
from datetime import datetime
import queue
import threading

# Configuration
file_name = 'car_listings_draft1.xlsx'
max_pages_per_url = 99
backup_interval = 1000  # Create backup after every 1000 cars
num_browsers = 10  # Number of concurrent browsers to use

# Create a queue to hold the URLs to be processed
url_queue = queue.Queue()

# Locks for thread-safe operations
excel_lock = threading.Lock()  # Lock for Excel file access
processed_ids_lock = threading.Lock()  # Lock for processed_ids access

# Function to generate URLs with different price brackets and year ranges
def generate_urls():
    urls = []
    
    # Year ranges from 2005-2006 to 2023-2024
    for year_start in range(2005, 2024):
        year_end = year_start + 1
        
        # Price brackets 
        for price_start in range(501, 20001, 100):
            price_end = price_start + 99
            
            # Skip the last bracket if it exceeds 20000
            if price_end > 20000:
                break
                
            url = (
                f"https://www.autotrader.co.uk/car-search?advertising-location=at_cars"
                f"&exclude-writeoff-categories=on&homeDeliveryAdverts=include"
                f"&postcode=mk404nu&price-from={price_start}&price-to={price_end}"
                f"&sort=relevance&year-from={year_start}&year-to={year_end}"
            )
            urls.append(url)
    
    return urls

def get_driver():
    """
    Initializes and returns a Selenium WebDriver instance with Chrome options.
    
    Returns:
        webdriver.Chrome: A Chrome WebDriver instance.
    """
    try:
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-dev-shm-usage")  # Overcome limited resource problems
        options.add_argument("--no-sandbox")  # Bypass OS security model
        options.add_argument("--disable-gpu")  # applicable to Windows OS only
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0")
        
        # Create the driver with a timeout
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        driver.set_page_load_timeout(30)  # Set page load timeout to 30 seconds
        
        return driver
    except Exception as e:
        print(f"Error creating driver: {e}")
        raise

def setup_excel():
    with excel_lock:  # Use lock to prevent concurrent access
        if not os.path.exists(file_name):
            wb = Workbook()
            sheet = wb.active
            sheet.title = 'Car Listings'
            sheet.append(['URL', 'Car ID', 'Make', 'Description', 'Price', 'Price Category', 
                        'Mileage', 'Registration', 'Source URL'])
            wb.save(file_name)
            print(f"Created new Excel file: {file_name}")

def save_to_excel(car_data):
    with excel_lock:  # Use lock to prevent concurrent access
        try:
            wb = openpyxl.load_workbook(file_name)
            sheet = wb.active
            for data in car_data:
                sheet.append(data)
            wb.save(file_name)
        except Exception as e:
            print(f"Error saving to Excel: {e}")

def create_csv_backup(total_scraped, browser_id=None):
    """
    Creates or updates a single CSV backup file with all data from the Excel file.
    
    Args:
        total_scraped (int): Total number of cars scraped so far
        browser_id (int, optional): ID of the browser that triggered the backup
    """
    backup_filename = 'car_listings_backup.csv'
    
    # Read data from Excel with lock to prevent concurrent access
    with excel_lock:
        try:
            wb = openpyxl.load_workbook(file_name)
            sheet = wb.active
            
            # Write to CSV (overwriting any existing file)
            with open(backup_filename, 'w', newline='', encoding='utf-8') as csvfile:
                csv_writer = csv.writer(csvfile)
                for row in sheet.rows:
                    csv_writer.writerow([cell.value for cell in row])
            
            print(f"Updated CSV backup: {backup_filename} (Browser {browser_id}, Total scraped: {total_scraped})")
        except Exception as e:
            print(f"Error creating CSV backup: {e}")

def get_car_ids_from_page(driver):
    soup = BeautifulSoup(driver.page_source, 'html.parser')
    
    # Check if we've reached the maximum results message
    max_results_message = "Maximum displayed results reached - Please refine your search to see different results."
    if max_results_message in driver.page_source:
        print("Maximum results reached message detected. Moving to next URL.")
        return [], True
    
    car_list = soup.find('ul', class_='at__sc-mddoqs-0 dsUIdv')
    results = []

    if car_list:
        cars = car_list.find_all('li')
        for car in cars:
            car_div = car.find('div', class_='at__sc-yv3gzn-0 gwDBhh')
            car_id = car_div.get('id') if car_div else None

            url = f"https://www.autotrader.co.uk/car-details/{car_id}?journey=FEATURED_LISTING_JOURNEY"

            make_tag = car.find('a', {'data-testid': 'search-listing-title'})
            make = make_tag.text.strip() if make_tag else None

            desc_tag = car.find('p', {'data-testid': 'search-listing-subtitle'})
            description = desc_tag.text.strip() if desc_tag else None

            price_tag = car.find('div', class_='at__sc-1n64n0d-8')
            price = price_tag.text.strip() if price_tag else None

            price_category = None
            ul = car.find('ul', class_='at__sc-u4ap7c-6 IEuDT')
            if ul:
                li_tags = ul.find_all('li')
                if li_tags:
                    text = li_tags[0].text.strip().lower()
                    if "fair price" in text:
                        price_category = "Fair price"
                    elif "good price" in text:
                        price_category = "Good price"
                    elif "great price" in text:
                        price_category = "Great price"
                    elif "lower price" in text:
                        price_category = "Lower price"
                    elif "higher price" in text:
                        price_category = "Higher price"

                    mileage, registration = None, None
                    for spec in li_tags:
                        text = spec.text.strip()
                        if 'miles' in text.lower():
                            mileage = text
                        elif 'reg' in text.lower():
                            registration = text


            if car_id:
                results.append([
                    url, car_id, make, description, price, price_category, mileage, registration, driver.current_url
                ])
    
    # Check if no cars were found (end of results)
    max_reached = False
    if not results:
        # Check if we're truly at the end or if there's just no results
        no_results = soup.find('h3', string='There are no exact matches for your search.')
        if not no_results:
            pagination = soup.find('nav', {'aria-label': 'Pagination'})
            if pagination:
                # If we have pagination but no results, we might be at max results
                max_reached = True
                
    return results, max_reached

def fetch_car_details_from_url(browser_id, processed_ids, total_scraped_ref):
    """
    Process function for each browser thread.
    
    Args:
        browser_id (int): ID of the browser/thread for logging
        processed_ids (set): Shared set of processed car IDs
        total_scraped_ref (list): Shared counter for total cars scraped
    """
    driver = None
    local_total = 0
    local_seen_ids = set()
    local_backup_count = 0

    try:
        # Initialize the driver
        try:
            driver = get_driver()
            print(f"Browser {browser_id} - Successfully initialized")
        except Exception as e:
            print(f"Browser {browser_id} - Failed to initialize: {e}")
            return
            
        while True:
            # Get the next URL from the queue with timeout
            try:
                base_url = url_queue.get(timeout=5)
                print(f"Browser {browser_id} processing URL: {base_url}")
            except queue.Empty:
                print(f"Browser {browser_id} - No more URLs to process, exiting")
                break
                
            page = 1
            url_scraped = 0
            
            while page <= max_pages_per_url:
                current_url = f"{base_url}&page={page}"
                print(f"Browser {browser_id} - Processing page {page} for URL: {base_url}")
                
                try:
                    driver.get(current_url)
                    time.sleep(uniform(1, 3))
                    
                    cars_data, max_reached = get_car_ids_from_page(driver)
                    
                    if not cars_data or max_reached:
                        if max_reached:
                            print(f"Browser {browser_id} - Maximum results reached for this URL. Moving to next URL.")
                        else:
                            print(f"Browser {browser_id} - No cars found on page {page} or reached the end. Moving to next URL.")
                        break
                    
                    new_data = []
                    for data in cars_data:
                        car_id = data[1]  # Car ID is at index 1
                        
                        # Thread-safe check if this car has already been processed
                        add_car = False
                        with processed_ids_lock:
                            if car_id not in processed_ids and car_id not in local_seen_ids:
                                processed_ids.add(car_id)
                                add_car = True
                        
                        if add_car:
                            local_seen_ids.add(car_id)
                            new_data.append(data)
                            local_total += 1
                            local_backup_count += 1
                            
                            # Thread-safe increment of total counter
                            with processed_ids_lock:
                                total_scraped_ref[0] += 1
                                total = total_scraped_ref[0]
                            
                            print(f"Browser {browser_id} - Collected car {car_id} | Total: {total}")
                    
                    if new_data:
                        save_to_excel(new_data)
                        url_scraped += len(new_data)
                        
                        # Create thread-specific CSV backup after every backup_interval cars
                        if local_backup_count >= backup_interval:
                            create_csv_backup(local_total, browser_id)
                            local_backup_count = 0
                    
                    page += 1
                except Exception as e:
                    print(f"Browser {browser_id} - Error processing page {page}: {e}")
                    break
            
            print(f"Browser {browser_id} - Completed URL: {base_url} - Scraped {url_scraped} cars")
            # Mark the task as done
            url_queue.task_done()
        
    except Exception as e:
        print(f"Browser {browser_id} - Thread error: {e}")
    finally:
        # Create final backup for this browser
        if local_backup_count > 0:
            try:
                create_csv_backup(local_total, browser_id)
            except Exception as e:
                print(f"Browser {browser_id} - Error creating final backup: {e}")
            
        print(f"Browser {browser_id} - Finished. Total scraped by this browser: {local_total}")
        
        # Safely quit the driver
        if driver:
            try:
                driver.quit()
            except Exception as e:
                print(f"Browser {browser_id} - Error quitting driver: {e}")

def main():
    # Generate all URLs
    urls_to_process = generate_urls()
    print(f"Generated {len(urls_to_process)} URLs to process")
    
    # Add all URLs to the queue
    for url in urls_to_process:
        url_queue.put(url)
    
    # Setup Excel file
    setup_excel()
    
    # Set to track already processed car IDs across all threads
    processed_ids = set()
    
    # Shared counter for total scraped cars
    total_scraped = [0]  # Using a list to make it mutable within threads
    
    # Print some statistics
    print(f"Starting scraping process with {num_browsers} browsers")
    print(f"Total URLs to process: {len(urls_to_process)}")
    print(f"Backup CSV files will be saved every {backup_interval} cars per browser")
    
    # Create and start browser threads
    threads = []
    for i in range(num_browsers):
        thread = threading.Thread(
            target=fetch_car_details_from_url,
            args=(i+1, processed_ids, total_scraped),
            name=f"Browser-{i+1}"
        )
        thread.daemon = True  # Allow the main program to exit even if threads are still running
        threads.append(thread)
        thread.start()
        # Small delay between starting browsers to avoid overwhelming the system
        time.sleep(2)
        print(f"Started browser thread {i+1}")
    
    try:
        # Wait for all URLs to be processed or timeout after 72 hours
        url_queue.join()
        print("All URLs have been processed")
    except KeyboardInterrupt:
        print("\nUser interrupted the process. Shutting down gracefully...")
    except Exception as e:
        print(f"Error in main thread: {e}")
    finally:
        print(f"Finishing up. Total cars scraped: {total_scraped[0]}")
        print(f"Scraped data saved to '{file_name}'")
        print(f"Check the working directory for backup CSV files.")
        
        # We don't need to join threads since they're daemon threads
        # and will terminate when main thread exits

if __name__ == "__main__":
    main()
